import vue from '@vitejs/plugin-vue';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import { defineConfig } from 'vite';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/js/app.ts'],
            ssr: 'resources/js/ssr.ts',
            refresh: true,
        }),
        tailwindcss(),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    server: {
        hmr: {
            host: 'localhost',
        }
    },
    build: {
        chunkSizeWarningLimit: 2000, // 提高警告阈值到 2MB
        rollupOptions: {
            output: {
                manualChunks: (id) => {
                    // Vue 生态系统
                    if (id.includes('vue') || id.includes('@inertiajs')) {
                        return 'vue-vendor';
                    }

                    // UI 组件库
                    if (id.includes('lucide-vue-next') || id.includes('reka-ui')) {
                        return 'ui-vendor';
                    }

                    // 代码高亮核心库
                    if (id.includes('highlight.js/lib/core')) {
                        return 'highlight-core';
                    }

                    // 代码高亮语言包
                    if (id.includes('highlight.js/lib/languages')) {
                        return 'highlight-languages';
                    }

                    // 其他 highlight.js 文件
                    if (id.includes('highlight.js')) {
                        return 'highlight-vendor';
                    }

                    // Markdown 处理
                    if (id.includes('markdown-it')) {
                        return 'markdown-vendor';
                    }

                    // 流式处理
                    if (id.includes('@laravel/stream-vue')) {
                        return 'stream-vendor';
                    }

                    // 工具库
                    if (id.includes('clsx') || id.includes('tailwind-merge') || id.includes('class-variance-authority')) {
                        return 'utils-vendor';
                    }

                    // 其他 node_modules 依赖
                    if (id.includes('node_modules')) {
                        return 'vendor';
                    }
                }
            }
        }
    }
});
