{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@laravel/echo-vue": "^2.1.7", "@types/node": "^22.13.5", "@vue/eslint-config-typescript": "^14.3.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "laravel-echo": "^2.1.7", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "pusher-js": "^8.4.0", "typescript-eslint": "^8.23.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@inertiajs/vue3": "^2.0.0", "@laravel/stream-vue": "^0.3.5", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-vue": "^6.0.0", "@vueuse/core": "^12.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "dexie": "^4.0.11", "highlight.js": "^11.11.1", "laravel-vite-plugin": "^2.0.0", "lucide-vue-next": "^0.468.0", "markdown-it": "^14.1.0", "reka-ui": "^2.2.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.2.5", "typescript": "^5.2.2", "vite": "^7.0.4", "vue": "^3.5.13", "ziggy-js": "^2.4.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}