<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use OpenAI;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ChatController extends Controller
{
    public function index()
    {
        return Inertia::render('Chat');
    }

    public function chat(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'context' => 'array',
            'context.*.role' => 'required|in:user,assistant',
            'context.*.content' => 'required|string',
        ]);

        return response()->stream(function () use ($request) {
            $client = OpenAI::client(config('services.openai.api_key'));
            
            // 构建包含上下文的消息数组
            $messages = [];
            
            // 添加上下文消息（如果提供）
            $context = $request->input('context', []);
            foreach ($context as $contextMessage) {
                $messages[] = [
                    'role' => $contextMessage['role'],
                    'content' => $contextMessage['content']
                ];
            }
            
            // 添加当前用户消息
            $messages[] = ['role' => 'user', 'content' => $request->input('message')];
            
            $response = $client->chat()->createStreamed([
                'model' => 'gpt-3.5-turbo',
                'messages' => $messages,
            ]);

            foreach ($response as $chunk) {
                if (!empty($chunk->choices[0]->delta->content)) {
                    echo $chunk->choices[0]->delta->content;
                    ob_flush();
                    flush();
                }
            }
        }, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}