# 聊天功能故障排除指南

## 当前状态

✅ **服务器运行正常**
- Laravel 服务器：http://localhost:8000 ✅
- Vite 开发服务器：http://localhost:5173 ✅

✅ **配置正确**
- OpenAI API 密钥已配置 ✅
- 路由配置正确 ✅
- CSRF token 设置正确 ✅

✅ **代码改进完成**
- 代码高亮功能已集成 ✅
- Markdown 渲染已实现 ✅
- 界面美化已完成 ✅
- MessageBubble 组件已创建 ✅

## 问题诊断

### 1. 检查页面加载
访问：http://localhost:8000/chat

**预期结果：**
- 页面正常加载
- 显示聊天界面
- 有欢迎消息和测试按钮

### 2. 检查浏览器控制台
打开开发者工具 (F12)，查看：

**Console 标签：**
- 是否有 JavaScript 错误
- 是否有网络请求错误
- 查看调试信息

**Network 标签：**
- 检查 POST /chat 请求
- 查看请求状态码
- 检查响应内容

### 3. 测试步骤

#### 步骤 1：基本页面测试
1. 访问 http://localhost:8000/chat
2. 确认页面加载无错误
3. 查看是否显示欢迎界面

#### 步骤 2：测试按钮功能
1. 点击"测试简单消息"按钮
2. 观察控制台输出
3. 检查是否发送网络请求

#### 步骤 3：手动输入测试
1. 在输入框输入："Hello"
2. 点击发送按钮
3. 观察响应

### 4. 常见问题及解决方案

#### 问题 1：页面无法加载
**症状：** 404 错误或页面空白
**解决：**
```bash
php artisan route:clear
php artisan config:clear
php artisan view:clear
```

#### 问题 2：CSRF Token 错误
**症状：** 419 Page Expired
**解决：**
1. 检查 meta 标签是否存在
2. 刷新页面获取新 token
3. 确认 CSRF 中间件配置

#### 问题 3：OpenAI API 错误
**症状：** 500 错误或无响应
**解决：**
1. 检查 API 密钥是否正确
2. 验证网络连接
3. 查看 Laravel 日志

#### 问题 4：前端依赖问题
**症状：** JavaScript 错误
**解决：**
```bash
npm install
npm run dev
```

### 5. 调试命令

#### 检查路由
```bash
php artisan route:list | grep chat
```

#### 检查配置
```bash
php artisan config:show services.openai
```

#### 查看日志
```bash
tail -f storage/logs/laravel.log
```

#### 测试 API
```bash
# 获取 CSRF token
curl -c cookies.txt http://localhost:8000/chat

# 提取 token 并测试 API
# (需要从页面获取实际的 CSRF token)
```

### 6. 预期的正常流程

1. **页面加载**
   - 显示聊天界面
   - 欢迎消息可见
   - 测试按钮可点击

2. **发送消息**
   - 控制台显示："Sending message: Hello"
   - 控制台显示 CSRF token
   - 网络请求发送到 /chat

3. **接收响应**
   - 控制台显示："Received chunk: ..."
   - AI 消息逐步显示
   - 最终显示："Stream finished"

4. **界面更新**
   - 消息气泡正确显示
   - 代码高亮正常工作
   - 自动滚动到底部

### 7. 成功标志

当一切正常时，您应该看到：

✅ 页面正常加载，无 JavaScript 错误
✅ 可以发送消息并收到 AI 回复
✅ 代码块有语法高亮
✅ Markdown 内容正确渲染
✅ 界面美观，交互流畅

### 8. 如果仍有问题

如果按照以上步骤仍无法解决问题：

1. **重启所有服务**
   ```bash
   # 停止所有进程
   # 重新启动
   npm run dev
   php artisan serve
   ```

2. **清除所有缓存**
   ```bash
   php artisan cache:clear
   php artisan route:clear
   php artisan config:clear
   php artisan view:clear
   ```

3. **检查依赖**
   ```bash
   composer install
   npm install
   ```

4. **查看完整错误日志**
   ```bash
   cat storage/logs/laravel.log
   ```

## 联系支持

如果问题持续存在，请提供：
- 浏览器控制台错误信息
- Laravel 日志内容
- 网络请求详情
- 具体的错误症状描述
