# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Frontend Development
- `pnpm run dev` - Start Vite development server
- `pnpm run build` - Build assets for production 
- `pnpm run build:ssr` - Build for server-side rendering
- `pnpm run lint` - Run ESLint with auto-fix
- `pnpm run format` - Format code with Prettier
- `pnpm run format:check` - Check code formatting

### Backend Development
- `composer dev` - Start full development environment (Laravel server, queue worker, logs, Vite)
- `composer dev:ssr` - Start development with SSR enabled
- `composer test` - Run PHP tests (clears config first)
- `php artisan serve` - Start Laravel development server
- `php artisan test` - Run PHPUnit/Pest tests
- `php artisan tinker` - Interactive PHP shell

### Testing
- Uses **Pest PHP** testing framework (not PHPUnit)
- Test files in `/tests/` directory with Feature and Unit subdirectories
- Run single test: `php artisan test --filter TestName`

## Architecture Overview

### Laravel + Vue.js with Inertia.js
- **Inertia.js** bridges Laravel backend and Vue.js frontend
- Single Blade template (`resources/views/app.blade.php`) serves all Vue components
- Server-side rendering (SSR) enabled via `resources/js/ssr.ts`
- Pages auto-resolved from `./pages/{name}.vue` using Laravel Vite helpers

### Frontend Architecture
- **Vue 3** with TypeScript and Composition API
- **shadcn-vue** component library built on Reka UI (Headless UI)
- **Tailwind CSS v4** with CSS variables for theming
- Import alias: `@/` → `resources/js/`

### Component Organization
```
resources/js/
├── components/ui/       # shadcn-vue UI components
├── components/          # App-specific components  
├── layouts/             # Layout components (app/, auth/, settings/)
├── pages/               # Inertia pages (route endpoints)
├── composables/         # Vue composables
├── lib/                 # Utilities (cn() function for class merging)
└── types/               # TypeScript definitions
```

### Layout System
- **App Layouts**: `AppLayout.vue` → `AppSidebarLayout.vue` → `AppShell.vue`
- **Auth Layouts**: `AuthLayout.vue` with simple/card/split variants
- **Settings Layouts**: Specialized layouts for settings pages
- Breadcrumbs passed through layout chain via props

### Authentication Flow
- Routes organized in `/routes/auth.php`
- Individual controllers for each auth operation
- Uses Inertia's `useForm` composable for form handling
- Middleware: `guest` for auth forms, `auth` for protected routes

### Theme System
- **Multi-layer approach**: Server-side (cookies) + client-side (localStorage)
- **HandleAppearance** middleware processes theme cookies
- **useAppearance** composable manages client-side theme state
- Supports light, dark, and system preferences
- SSR-compatible (prevents theme flash)

### State Management
- **No global state store** - uses Inertia page props and composables
- Shared data via `HandleInertiaRequests` middleware
- Form state managed by Inertia's `useForm` composable

## Key Conventions

### Component Development
- Use `defineProps<Interface>()` for TypeScript prop definitions
- Import layout components in pages and pass breadcrumbs
- UI components follow shadcn-vue patterns with `cn()` utility for class merging
- Error handling via `InputError.vue` component

### Route Structure  
- Laravel routes map 1:1 to Vue pages in `resources/js/pages/`
- Route organization: `/routes/web.php` (main), `/routes/auth.php`, `/routes/settings.php`
- Ziggy provides Laravel routes in Vue via `route()` helper

### TypeScript Usage
- Strict mode enabled with comprehensive type checking
- Page props typed with generic `AppPageProps` interface
- Component props use interface-based definitions
- Ziggy provides Laravel route typing

### CSS and Styling
- Tailwind CSS with `@/` prefix for utilities
- Use `cn()` function from `@/lib/utils` to merge conditional classes
- CSS variables for theming defined in `resources/css/app.css`

## Development Notes

### Middleware Important
- `HandleInertiaRequests` shares global data (auth user, sidebar state, etc.)
- `HandleAppearance` processes theme preferences for SSR compatibility

### Build Configuration
- Vite configuration in `vite.config.ts` includes Laravel plugin and Vue plugin
- Entry point: `resources/js/app.ts`
- SSR entry: `resources/js/ssr.ts`
- Assets build to `public/build/`

### Package Management
- Uses **pnpm** for package management (not npm)
- Laravel Vite plugin handles asset compilation
- Optional dependencies for platform-specific optimization

## Access Information
- Application accessible at http://cherry.test
- No need to start additional servers for local development
- Uses SQLite database (database/database.sqlite)

*** 重要：本项目使用pnpm 进行包管理，使用 http://cherry.test 进行访问，不需要启动服务器 所有页面元素禁止使用shadow阴影效果 ***