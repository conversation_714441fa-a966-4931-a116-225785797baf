<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天数据库修复工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .success {
            background: #28a745;
        }
        .info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .step {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 聊天数据库修复工具</h1>
    
    <div class="warning">
        <strong>⚠️ 注意:</strong> 如果你在聊天应用中遇到了数据库相关的错误，请按照以下步骤进行修复。
    </div>

    <h3>🔄 简单修复方案</h3>

    <div class="step">
        <h4>方法 1：使用浏览器开发者工具（推荐）</h4>
        <ol>
            <li>访问聊天页面：<a href="/chat" target="_blank">http://cherry.test/chat</a></li>
            <li>按 F12 打开开发者工具</li>
            <li>切换到 "Console" 标签页</li>
            <li>输入以下命令之一：</li>
        </ol>
        
        <div class="info">
            <strong>测试数据库：</strong><br>
            <code>window.testDatabase()</code>
            
            <br><br><strong>清理损坏数据：</strong><br>
            <code>window.cleanupDatabase()</code>
            
            <br><br><strong>完全重置数据库（删除所有数据）：</strong><br>
            <code>window.resetDatabase()</code>
        </div>
    </div>

    <div class="step">
        <h4>方法 2：清除浏览器数据</h4>
        <ol>
            <li>在 Chrome 中：设置 → 隐私设置和安全性 → 清除浏览数据</li>
            <li>选择"高级"选项卡</li>
            <li>勾选"Cookies 和其他网站数据"</li>
            <li>在"网站数据"中输入 <code>cherry.test</code></li>
            <li>点击"清除数据"</li>
        </ol>
    </div>

    <div class="step">
        <h4>方法 3：手动清除 IndexedDB</h4>
        <ol>
            <li>按 F12 打开开发者工具</li>
            <li>切换到 "Application" 标签页</li>
            <li>在左侧找到 "Storage" → "IndexedDB"</li>
            <li>找到 "ChatDatabase" 并右键删除</li>
            <li>刷新聊天页面</li>
        </ol>
    </div>

    <h3>📋 错误类型对应解决方案</h3>

    <div class="info">
        <strong>如果看到以下错误：</strong>
        <ul>
            <li><code>DataError: Failed to execute 'bound' on 'IDBKeyRange'</code> → 使用方法 1 的清理命令</li>
            <li><code>Cannot read properties of null</code> → 使用方法 1 的重置命令</li>
            <li><code>Database connection error</code> → 使用方法 2 清除浏览器数据</li>
            <li>其他数据库相关错误 → 依次尝试方法 1、2、3</li>
        </ul>
    </div>

    <h3>🆘 需要帮助？</h3>
    
    <div class="info">
        如果上述方法都无法解决问题，请：
        <ol>
            <li>截图错误信息</li>
            <li>记录操作步骤</li>
            <li>尝试在不同浏览器中测试</li>
        </ol>
    </div>

    <div class="step">
        <p><strong>💡 提示：</strong> 大多数数据库问题都可以通过在控制台运行 <code>window.cleanupDatabase()</code> 来解决。这是最安全的修复方法。</p>
    </div>

    <script>
        // 检查是否在聊天页面，如果是则提供快捷按钮
        if (window.location.pathname === '/chat') {
            document.body.innerHTML += `
                <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
                    <button class="button" onclick="window.cleanupDatabase && window.cleanupDatabase()">
                        🧹 清理数据库
                    </button>
                    <button class="button danger" onclick="window.resetDatabase && window.resetDatabase()">
                        🔄 重置数据库
                    </button>
                </div>
            `;
        }
    </script>
</body>
</html>