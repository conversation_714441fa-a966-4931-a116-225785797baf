<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天数据库快速测试</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #f0f0f0;
        }
        .result {
            background: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
    </style>
</head>
<body>
    <h1>🧪 聊天数据库快速测试</h1>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(message, type = 'result') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            results.appendChild(div);
        }

        async function testChatDatabase() {
            addResult('开始测试聊天数据库...', 'result');
            
            try {
                // 等待页面完全加载
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 测试数据库函数是否可用
                if (typeof window.testDatabase === 'function') {
                    addResult('✅ 数据库测试函数已加载', 'success');
                    
                    try {
                        await window.testDatabase();
                        addResult('✅ 数据库测试成功完成', 'success');
                    } catch (error) {
                        addResult('❌ 数据库测试失败: ' + error.message, 'error');
                        
                        // 尝试清理数据库
                        if (typeof window.cleanupDatabase === 'function') {
                            addResult('🧹 尝试清理数据库...', 'result');
                            try {
                                await window.cleanupDatabase();
                                addResult('✅ 数据库清理成功', 'success');
                            } catch (cleanupError) {
                                addResult('❌ 数据库清理失败: ' + cleanupError.message, 'error');
                            }
                        }
                    }
                } else {
                    addResult('❌ 数据库测试函数未加载', 'error');
                }
                
            } catch (error) {
                addResult('❌ 测试过程出错: ' + error.message, 'error');
            }
            
            // 提供手动操作建议
            addResult('', 'result');
            addResult('💡 如果测试失败，请在控制台手动运行:', 'result');
            addResult('   window.cleanupDatabase() - 清理数据库', 'result');
            addResult('   window.resetDatabase() - 重置数据库', 'result');
        }

        // 页面加载后自动开始测试
        window.addEventListener('load', testChatDatabase);
    </script>
</body>
</html>