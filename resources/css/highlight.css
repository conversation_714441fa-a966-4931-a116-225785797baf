/* Highlight.js 代码高亮样式 - VS Code Dark 主题 */
.hljs {
    display: block;
    overflow-x: hidden;
    overflow-wrap: break-word;
    word-wrap: break-word;
    white-space: pre-wrap;
    padding: 1rem;
    background: #1e1e1e;
    color: #d4d4d4;
    border-radius: 0.5rem;
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    position: relative;
}

.hljs-keyword,
.hljs-operator,
.hljs-pattern-match {
    color: #569cd6;
    font-weight: bold;
}

.hljs-function,
.hljs-class,
.hljs-title {
    color: #dcdcaa;
}

.hljs-string,
.hljs-char {
    color: #ce9178;
}

.hljs-number,
.hljs-literal {
    color: #b5cea8;
}

.hljs-comment {
    color: #6a9955;
    font-style: italic;
}

.hljs-variable,
.hljs-template-variable {
    color: #9cdcfe;
}

.hljs-type,
.hljs-built_in {
    color: #4ec9b0;
}

.hljs-property,
.hljs-attribute {
    color: #92c5f8;
}

.hljs-tag {
    color: #569cd6;
}

.hljs-name {
    color: #92c5f8;
}

.hljs-selector-id,
.hljs-selector-class {
    color: #d7ba7d;
}

.hljs-meta {
    color: #9cdcfe;
}

.hljs-doctag {
    color: #608b4e;
}

.hljs-params {
    color: #9cdcfe;
}

.hljs-symbol,
.hljs-bullet {
    color: #569cd6;
}

.hljs-link {
    color: #569cd6;
    text-decoration: underline;
}

.hljs-section {
    color: #dcdcaa;
    font-weight: bold;
}

.hljs-quote {
    color: #6a9955;
    font-style: italic;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}

.hljs-deletion {
    background-color: #f92672;
    color: white;
}

.hljs-addition {
    background-color: #a6e22e;
    color: black;
}

/* 特定语言的样式 */
.hljs.language-javascript .hljs-keyword {
    color: #f92672;
}

.hljs.language-python .hljs-keyword {
    color: #66d9ef;
}

.hljs.language-css .hljs-selector-tag {
    color: #f92672;
}

.hljs.language-html .hljs-tag {
    color: #f92672;
}

/* 行号样式（如果需要） */
.hljs-ln {
    border-collapse: collapse;
}

.hljs-ln td {
    padding: 0;
}

.hljs-ln-n {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-align: center;
    color: #6a9955;
    border-right: 1px solid #3c3c3c;
    vertical-align: top;
    padding-right: 8px;
}

.hljs-ln-code {
    padding-left: 10px;
}

/* 复制按钮样式 */
.copy-button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.375rem;
    background-color: rgba(55, 65, 81, 0.9);
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease-in-out;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    min-height: 32px;
}

.hljs:hover .copy-button,
.hljs.group:hover .copy-button {
    opacity: 1;
}

.copy-button:hover {
    background-color: rgba(75, 85, 99, 0.95);
    transform: scale(1.05);
}

.copy-button:active {
    transform: scale(0.95);
}

.copy-button.copied {
    background-color: rgba(34, 197, 94, 0.9);
}

.copy-button svg {
    width: 16px;
    height: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .hljs {
        font-size: 0.75rem;
        padding: 0.75rem;
    }
    
    .copy-button {
        padding: 0.25rem;
        top: 0.25rem;
        right: 0.25rem;
    }
}

/* 暗色主题适配 */
.dark .hljs {
    background: #0d1117;
    border: 1px solid #30363d;
}

.dark .hljs-ln-n {
    border-right-color: #30363d;
}

/* 代码块内容样式优化 */
.hljs code {
    white-space: pre-wrap;
    word-break: break-word;
    overflow-wrap: break-word;
}
