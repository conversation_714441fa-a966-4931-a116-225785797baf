<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useChatHistory, type Message } from '@/composables/useChatHistory';
import { computed } from 'vue';

interface Props {
  isCollapsed?: boolean;
}

defineProps<Props>();

const emit = defineEmits<{
  sessionChanged: [sessionId: number | null, messages: Message[]];
  newSession: [];
  databaseError: [error: Error];
}>();

const {
  currentSessionId,
  sessions,
  messages,
  isLoading,
  createSession,
  switchToSession,
  deleteSession
} = useChatHistory();

// 处理新建会话
const handleNewSession = async () => {
  try {
    const newSessionId = await createSession();
    console.log('新会话已创建，会话ID:', newSessionId);
    
    // 直接通知父组件，不需要在这里处理消息
    // 新会话应该是空的，所以传递空数组
    emit('sessionChanged', newSessionId, []);
    emit('newSession');
  } catch (error) {
    console.error('创建新会话失败:', error);
    emit('databaseError', error as Error);
  }
};

// 处理切换会话
const handleSwitchSession = async (sessionId: number) => {
  try {
    await switchToSession(sessionId);
    // 给UI一些时间来更新
    await new Promise(resolve => setTimeout(resolve, 100));
    emit('sessionChanged', currentSessionId.value, messages.value);
  } catch (error) {
    console.error('切换会话失败:', error);
    emit('databaseError', error as Error);
  }
};

// 处理删除会话
const handleDeleteSession = async (sessionId: number, event: Event) => {
  event.stopPropagation();
  if (confirm('确定要删除这个对话吗？此操作无法恢复。')) {
    try {
      await deleteSession(sessionId);
      emit('sessionChanged', currentSessionId.value, messages.value);
    } catch (error) {
      console.error('删除会话失败:', error);
      emit('databaseError', error as Error);
    }
  }
};

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  } else if (days === 1) {
    return '昨天';
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  }
};

// 排序会话
const sortedSessions = computed(() => {
  // 创建副本避免直接修改原数组
  const sessionsCopy = [...sessions.value];
  return sessionsCopy.sort((a, b) => {
    // 当前活跃会话排在最前面
    if (a.isActive && !b.isActive) return -1;
    if (!a.isActive && b.isActive) return 1;
    // 其余按更新时间倒序
    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
  });
});
</script>

<template>
  <Card class="h-full flex flex-col">
    <CardHeader class="flex-shrink-0 pb-3">
      <div class="flex items-center justify-between">
        <CardTitle class="text-lg">对话历史</CardTitle>
        <Button @click="handleNewSession" size="sm" class="shrink-0">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          <span v-if="!isCollapsed">新对话</span>
        </Button>
      </div>
    </CardHeader>
    
    <CardContent class="flex-1 overflow-hidden p-0">
      <div class="h-full overflow-y-auto px-4 pb-4">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="space-y-2">
          <Skeleton v-for="i in 3" :key="i" class="h-16 w-full" />
        </div>
        
        <!-- 会话列表 -->
        <div v-else-if="sortedSessions.length > 0" class="space-y-2">
          <div
            v-for="session in sortedSessions"
            :key="session.id"
            @click="handleSwitchSession(session.id!)"
            class="group relative p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:bg-muted/50"
            :class="{
              'bg-primary/10 border-primary/20': session.id === currentSessionId,
              'border-border': session.id !== currentSessionId
            }"
          >
            <!-- 活跃指示器 -->
            <div
              v-if="session.isActive"
              class="absolute top-3 right-3 w-2 h-2 bg-green-500 rounded-full"
            />
            
            <div class="space-y-1">
              <div class="font-medium text-sm line-clamp-1" :title="session.title">
                {{ session.title }}
              </div>
              <div class="text-xs text-muted-foreground">
                {{ formatTime(new Date(session.updatedAt)) }}
              </div>
            </div>
            
            <!-- 删除按钮 -->
            <Button
              @click="(e) => handleDeleteSession(session.id!, e)"
              variant="ghost"
              size="sm"
              class="absolute top-1 right-6 opacity-0 group-hover:opacity-100 transition-opacity w-6 h-6 p-0 text-muted-foreground hover:text-destructive"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </Button>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-else class="text-center py-8 text-muted-foreground">
          <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          <p class="text-sm">暂无对话历史</p>
          <p class="text-xs mt-1">点击"新对话"开始</p>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<style scoped>
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
</style>