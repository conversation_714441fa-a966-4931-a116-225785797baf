<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { renderMessage, hasCodeBlocks } from '@/utils/messageRenderer';
// 注意：Copy, Check, Button 等组件通过 DOM 字符串模板使用，不需要导入

// 导入图标组件
import { User, Bot } from 'lucide-vue-next';

interface Props {
    role: 'user' | 'assistant';
    content: string;
    isStreaming?: boolean;
    timestamp?: Date;
    status?: 'sending' | 'sent' | 'error';
}

const props = defineProps<Props>();

const messageRef = ref<HTMLElement>();
const copiedStates = ref<Map<number, boolean>>(new Map());
const renderedContent = ref<string>('');

// 异步渲染内容
const updateRenderedContent = async () => {
    if (props.role === 'user') {
        // 用户消息保持纯文本，但支持换行
        renderedContent.value = props.content.replace(/\n/g, '<br>');
    } else {
        // AI 消息支持 Markdown 和代码高亮
        renderedContent.value = await renderMessage(props.content);
    }
};

// 监听内容变化
watch(() => props.content, updateRenderedContent, { immediate: true });

const hasCode = computed(() => hasCodeBlocks(props.content));

// 复制代码块功能
const copyCodeBlock = async (code: string, index: number) => {
    try {
        await navigator.clipboard.writeText(code);
        copiedStates.value.set(index, true);
        setTimeout(() => {
            copiedStates.value.set(index, false);
        }, 2000);
    } catch (err) {
        console.error('Failed to copy code:', err);
    }
};

// 为代码块添加复制按钮
const addCopyButtons = () => {
    if (!messageRef.value) return;
    
    const codeBlocks = messageRef.value.querySelectorAll('pre.hljs');
    codeBlocks.forEach((block, index) => {
        // 避免重复添加按钮
        if (block.querySelector('.copy-button')) return;
        
        const code = block.querySelector('code')?.textContent || '';
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-button';
        copyButton.innerHTML = `
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
        `;

        copyButton.addEventListener('click', async () => {
            try {
                await copyCodeBlock(code, index);
                copyButton.innerHTML = `
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                `;
                copyButton.classList.add('copied');
                setTimeout(() => {
                    copyButton.innerHTML = `
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    `;
                    copyButton.classList.remove('copied');
                }, 2000);
            } catch (err) {
                console.error('Failed to copy code:', err);
            }
        });

        // 为代码块添加相对定位和组样式
        block.classList.add('relative', 'group');
        block.appendChild(copyButton);
    });
};

onMounted(() => {
    if (props.role === 'assistant' && hasCode.value) {
        // 延迟添加复制按钮，确保内容已渲染
        setTimeout(addCopyButtons, 100);
    }
});

// 监听内容变化（流式输出时）
const observer = ref<MutationObserver>();

onMounted(() => {
    if (props.role === 'assistant' && messageRef.value) {
        observer.value = new MutationObserver(() => {
            if (hasCode.value) {
                addCopyButtons();
            }
        });
        
        observer.value.observe(messageRef.value, {
            childList: true,
            subtree: true,
            characterData: true
        });
    }
});
</script>

<template>
    <div 
        class="flex items-start gap-3 group"
        :class="role === 'user' ? 'flex-row-reverse' : 'flex-row'"
    >
        <!-- 头像 -->
        <div 
            class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center"
            :class="role === 'user' 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted border border-border'"
        >
            <User v-if="role === 'user'" class="w-4 h-4" />
            <Bot v-else class="w-4 h-4" />
        </div>
        
        <!-- 消息内容 -->
        <div
            class="flex-1"
            :class="role === 'user' ? 'text-right' : 'text-left'"
        >
            <!-- 角色标签 -->
            <div class="text-xs text-muted-foreground mb-1">
                {{ role === 'user' ? '你' : 'AI助手' }}
            </div>
            
            <!-- 消息气泡 -->
            <div
                ref="messageRef"
                class="rounded-lg px-4 py-3 inline-block max-w-full"
                :class="[
                    role === 'user'
                        ? 'bg-primary text-primary-foreground ml-auto max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] xl:max-w-[45%]'
                        : 'bg-card border border-border max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] xl:max-w-[45%]'
                ]"
            >
                <!-- 用户消息 -->
                <div
                    v-if="role === 'user'"
                    class="whitespace-pre-wrap break-words word-break overflow-wrap-anywhere"
                    v-html="renderedContent"
                />

                <!-- AI消息 -->
                <div
                    v-else
                    class="prose prose-sm max-w-none dark:prose-invert break-words word-break overflow-wrap-anywhere"
                    :class="{
                        'prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm': true
                    }"
                    v-html="renderedContent"
                />
                
                <!-- 流式输出指示器 -->
                <div v-if="isStreaming && role === 'assistant'" class="flex items-center mt-2">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-current rounded-full animate-bounce opacity-60"></div>
                        <div class="w-2 h-2 bg-current rounded-full animate-bounce opacity-60" style="animation-delay: 0.1s;"></div>
                        <div class="w-2 h-2 bg-current rounded-full animate-bounce opacity-60" style="animation-delay: 0.2s;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 消息时间戳和状态 -->
            <div class="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                <span>
                    {{ (props.timestamp || new Date()).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}
                </span>

                <!-- 消息状态 -->
                <div v-if="props.role === 'user'" class="flex items-center">
                    <svg v-if="props.status === 'sending'" class="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <svg v-else-if="props.status === 'sent'" class="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <svg v-else-if="props.status === 'error'" class="w-3 h-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>

                <!-- AI 回复状态 -->
                <div v-if="props.role === 'assistant' && props.isStreaming" class="flex items-center gap-1 text-primary">
                    <div class="w-2 h-2 bg-current rounded-full animate-pulse"></div>
                    <span>正在输入...</span>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 代码高亮样式 */
:deep(.hljs) {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-radius: 0.375rem;
    padding: 1rem;
    overflow-x: hidden;
    overflow-wrap: break-word;
    word-wrap: break-word;
    white-space: pre-wrap;
    position: relative;
}

:deep(.hljs-keyword) {
    color: #569cd6;
}

:deep(.hljs-string) {
    color: #ce9178;
}

:deep(.hljs-number) {
    color: #b5cea8;
}

:deep(.hljs-comment) {
    color: #6a9955;
    font-style: italic;
}

:deep(.hljs-function) {
    color: #dcdcaa;
}

:deep(.hljs-variable) {
    color: #9cdcfe;
}

/* Prose 样式调整 */
:deep(.prose) {
    color: inherit;
}

:deep(.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6) {
    color: inherit;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

:deep(.prose h1:first-child, .prose h2:first-child, .prose h3:first-child,
       .prose h4:first-child, .prose h5:first-child, .prose h6:first-child) {
    margin-top: 0;
}

:deep(.prose p) {
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

:deep(.prose ul, .prose ol) {
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
}

:deep(.prose li) {
    margin-bottom: 0.25rem;
}

:deep(.prose ul li) {
    list-style-type: disc;
}

:deep(.prose ol li) {
    list-style-type: decimal;
}

:deep(.prose blockquote) {
    border-left: 4px solid hsl(var(--border));
    background-color: hsl(var(--muted) / 0.5);
    padding-left: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    margin: 0.75rem 0;
    font-style: italic;
}

:deep(.prose table) {
    border-collapse: collapse;
    border: 1px solid hsl(var(--border));
    width: 100%;
    margin: 1rem 0;
}

:deep(.prose th, .prose td) {
    border: 1px solid hsl(var(--border));
    padding: 0.75rem;
    text-align: left;
}

:deep(.prose th) {
    background-color: hsl(var(--muted));
    font-weight: 600;
}

:deep(.prose code) {
    background-color: hsl(var(--muted));
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

:deep(.prose pre) {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    margin: 1rem 0;
}

:deep(.prose pre code) {
    background-color: transparent;
    padding: 0;
    color: inherit;
}

:deep(.prose a) {
    color: hsl(var(--primary));
    text-decoration: underline;
}

:deep(.prose a:hover) {
    text-decoration: none;
}

:deep(.prose strong) {
    font-weight: 600;
}

:deep(.prose em) {
    font-style: italic;
}

:deep(.prose hr) {
    border-top: 1px solid hsl(var(--border));
    margin: 1.5rem 0;
}

/* 强制换行样式 */
.word-break {
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.overflow-wrap-anywhere {
    overflow-wrap: anywhere;
}
</style>
