<script setup lang="ts">
import { ref } from 'vue';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

// 主题选择
const currentTheme = ref('system');

const themes = [
  { id: 'light', name: '浅色模式', description: '适合白天使用' },
  { id: 'dark', name: '深色模式', description: '适合夜晚使用' },
  { id: 'system', name: '跟随系统', description: '自动切换' }
];

const setTheme = (themeId: string) => {
  currentTheme.value = themeId;
  // 这里可以添加主题切换逻辑
  console.log('切换主题到:', themeId);
};
</script>

<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-sm font-medium mb-3">主题设置</h3>
      <div class="grid grid-cols-1 gap-3">
        <div
          v-for="theme in themes"
          :key="theme.id"
          class="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
          :class="{ 'border-primary bg-muted': currentTheme === theme.id }"
          @click="setTheme(theme.id)"
        >
          <div class="flex-shrink-0">
            <div
              class="w-4 h-4 rounded-full border-2 border-primary"
              :class="{ 'bg-primary': currentTheme === theme.id }"
            ></div>
          </div>
          <div class="flex-1">
            <Label class="cursor-pointer">{{ theme.name }}</Label>
            <p class="text-sm text-muted-foreground">{{ theme.description }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="pt-4 border-t">
      <Button @click="setTheme(currentTheme)" size="sm">
        应用设置
      </Button>
    </div>
  </div>
</template>