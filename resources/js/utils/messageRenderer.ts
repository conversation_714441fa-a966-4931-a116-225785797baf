import MarkdownIt from 'markdown-it';

// 动态导入 highlight.js
let hljs: any = null;
const loadHighlightJs = async () => {
    if (!hljs) {
        hljs = await import('highlight.js');
    }
    return hljs;
};

// 配置 markdown-it 实例
const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    highlight: function (str, lang) {
        // 如果 hljs 还没有加载，返回普通的代码块
        if (!hljs) {
            return `<pre class="hljs"><code class="hljs">${md.utils.escapeHtml(str)}</code></pre>`;
        }

        if (lang && hljs.getLanguage && hljs.getLanguage(lang)) {
            try {
                return `<pre class="hljs"><code class="hljs language-${lang}">${hljs.highlight(str, { language: lang }).value}</code></pre>`;
            } catch {
                // 高亮失败时使用默认处理
            }
        }
        return `<pre class="hljs"><code class="hljs">${md.utils.escapeHtml(str)}</code></pre>`;
    }
});

/**
 * 渲染消息内容，支持 Markdown 和代码高亮
 */
export async function renderMessage(content: string): Promise<string> {
    // 如果内容包含代码块，先加载 highlight.js
    if (hasCodeBlocks(content)) {
        await loadHighlightJs();
    }
    return md.render(content);
}

/**
 * 检测消息是否包含代码块
 */
export function hasCodeBlocks(content: string): boolean {
    return /```[\s\S]*?```/.test(content) || /`[^`\n]+`/.test(content);
}

/**
 * 提取消息中的代码语言
 */
export function extractCodeLanguages(content: string): string[] {
    const languages: string[] = [];
    const codeBlockRegex = /```(\w+)?\n[\s\S]*?```/g;
    let match;
    
    while ((match = codeBlockRegex.exec(content)) !== null) {
        if (match[1]) {
            languages.push(match[1]);
        }
    }
    
    return [...new Set(languages)]; // 去重
}
