// 数据库测试工具 - 仅用于开发调试
import { chatDb, checkDatabaseHealth, resetChatDatabase, cleanupCorruptedData } from '@/lib/chatDatabase';

// 测试数据库基本操作
export const testDatabaseOperations = async () => {
  console.log('🔧 开始数据库测试...');
  
  try {
    // 1. 健康检查
    console.log('📋 检查数据库健康状态...');
    const isHealthy = await checkDatabaseHealth();
    console.log(`数据库健康状态: ${isHealthy ? '✅ 正常' : '❌ 异常'}`);
    
    // 2. 测试基本查询
    console.log('📊 测试基本查询...');
    const sessionCount = await chatDb.sessions.count();
    const messageCount = await chatDb.messages.count();
    console.log(`现有会话数: ${sessionCount}`);
    console.log(`现有消息数: ${messageCount}`);
    
    // 3. 测试创建操作
    console.log('➕ 测试创建操作...');
    const testSession = await chatDb.sessions.add({
      title: '测试会话',
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: false
    });
    console.log(`创建测试会话 ID: ${testSession}`);
    
    // 4. 测试查询操作
    console.log('🔍 测试查询操作...');
    const sessions = await chatDb.sessions.where('isActive').equals(false).toArray();
    console.log(`查询到 ${sessions.length} 个非活跃会话`);
    
    // 5. 清理测试数据
    console.log('🧹 清理测试数据...');
    await chatDb.sessions.delete(testSession as number);
    console.log('测试数据已清理');
    
    console.log('✅ 数据库测试完成，一切正常！');
    return true;
    
  } catch (error) {
    console.error('❌ 数据库测试失败:', error);
    return false;
  }
};

// 清理数据库损坏数据
export const cleanupDatabase = async () => {
  console.log('🧹 清理数据库...');
  
  try {
    const success = await cleanupCorruptedData();
    if (success) {
      console.log('✅ 数据库清理成功');
      return await testDatabaseOperations();
    } else {
      console.log('❌ 数据库清理失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 数据库清理出错:', error);
    return false;
  }
};

// 重置并重新测试数据库
export const resetAndTestDatabase = async () => {
  console.log('🔄 重置数据库...');
  
  try {
    await resetChatDatabase();
    console.log('✅ 数据库重置成功');
    
    // 重新测试
    return await testDatabaseOperations();
  } catch (error) {
    console.error('❌ 数据库重置失败:', error);
    return false;
  }
};

// 在开发环境下将测试函数暴露到全局
if (import.meta.env.DEV) {
  (window as any).testDatabase = testDatabaseOperations;
  (window as any).cleanupDatabase = cleanupDatabase;
  (window as any).resetDatabase = resetAndTestDatabase;
  console.log('🛠️  数据库测试工具已加载到全局:');
  console.log('  - window.testDatabase(): 测试数据库操作');
  console.log('  - window.cleanupDatabase(): 清理损坏的数据');
  console.log('  - window.resetDatabase(): 重置并测试数据库');
}