<script setup lang="ts">
// 独立的聊天页面布局，不依赖后台框架
interface Props {
  title?: string;
}

withDefaults(defineProps<Props>(), {
  title: '<PERSON> Chat'
});
</script>

<template>
  <div class="min-h-screen bg-background">
    <!-- 简单的顶部导航 -->
    <header class="border-b border-border bg-card">
      <div class="px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h1 class="text-xl font-semibold">{{ title }}</h1>
          </div>
          
          <!-- 用户菜单 -->
          <nav class="flex items-center space-x-4">
            <a 
              href="/settings/profile" 
              class="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              设置
            </a>
            <form method="post" action="/logout" class="inline">
              <button 
                type="submit"
                class="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                退出
              </button>
            </form>
          </nav>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="h-[calc(100vh-73px)]">
      <slot />
    </main>
  </div>
</template>