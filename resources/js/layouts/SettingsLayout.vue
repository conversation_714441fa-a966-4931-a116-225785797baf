<script setup lang="ts">
// 简单的设置页面布局
interface Props {
  title?: string;
}

withDefaults(defineProps<Props>(), {
  title: '用户设置'
});
</script>

<template>
  <div class="min-h-screen bg-background">
    <!-- 简单的顶部导航 -->
    <header class="border-b border-border bg-card">
      <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <a 
              href="/chat" 
              class="flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              <span>返回聊天</span>
            </a>
            <div class="text-sm text-muted-foreground">/</div>
            <h1 class="text-lg font-medium">{{ title }}</h1>
          </div>
          
          <!-- 用户菜单 -->
          <nav class="flex items-center space-x-4">
            <form method="post" action="/logout" class="inline">
              <button 
                type="submit"
                class="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                退出
              </button>
            </form>
          </nav>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-6">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- 侧边栏导航 -->
        <nav class="lg:col-span-1">
          <div class="bg-card rounded-lg border p-4">
            <h2 class="font-medium mb-3">设置选项</h2>
            <ul class="space-y-2">
              <li>
                <a 
                  href="/settings/profile"
                  class="block px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors"
                  :class="{ 'bg-muted text-foreground': $page.url === '/settings/profile' }"
                >
                  个人资料
                </a>
              </li>
              <li>
                <a 
                  href="/settings/password"
                  class="block px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors"
                  :class="{ 'bg-muted text-foreground': $page.url === '/settings/password' }"
                >
                  修改密码
                </a>
              </li>
              <li>
                <a 
                  href="/settings/appearance"
                  class="block px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors"
                  :class="{ 'bg-muted text-foreground': $page.url === '/settings/appearance' }"
                >
                  外观设置
                </a>
              </li>
            </ul>
          </div>
        </nav>
        
        <!-- 内容区域 -->
        <main class="lg:col-span-3">
          <div class="bg-card rounded-lg border p-6">
            <slot />
          </div>
        </main>
      </div>
    </div>
  </div>
</template>