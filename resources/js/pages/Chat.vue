<script setup lang="ts">
import ChatLayout from '@/layouts/ChatLayout.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import MessageBubble from '@/components/chat/MessageBubble.vue';
import ChatSessionList from '@/components/chat/ChatSessionList.vue';
import { Head } from '@inertiajs/vue3';
import { useStream } from '@laravel/stream-vue';
import { useChatHistory, type Message } from '@/composables/useChatHistory';
import { ref, nextTick, computed } from 'vue';

const currentMessage = ref('');
const messagesContainer = ref<HTMLElement>();
const inputHistory = ref<string[]>([]);
const historyIndex = ref(-1);
const isTyping = ref(false);
const showSessionList = ref(true);

// 使用聊天历史 composable
const {
    currentSessionId,
    messages,
    saveMessage,
    updateMessageStatus,
    clearCurrentSession,
    createSession,
    initializeDatabase,
    loadMessages
} = useChatHistory();

// 数据库错误状态
const dbError = ref(false);
const isResettingDb = ref(false);

// 重置数据库
const resetDatabase = async () => {
    if (isResettingDb.value) return;
    
    isResettingDb.value = true;
    try {
        await initializeDatabase();
        dbError.value = false;
        // 重新加载数据
        window.location.reload();
    } catch (error) {
        console.error('重置数据库失败:', error);
        dbError.value = true;
    } finally {
        isResettingDb.value = false;
    }
};

// Get CSRF token from meta tag
const getCsrfToken = () => {
    const meta = document.querySelector('meta[name="csrf-token"]');
    return meta?.getAttribute('content') || '';
};

const { isFetching, isStreaming, send, clearData } = useStream('/chat', {
    csrfToken: getCsrfToken(),
    onData: (chunk: string) => {
        console.log('Received chunk:', chunk);
        const lastMessage = messages.value[messages.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content += chunk;
            isTyping.value = true;
            // 自动滚动到底部
            nextTick(() => {
                scrollToBottom();
            });
        }
    },
    onFinish: async () => {
        console.log('Stream finished');
        isTyping.value = false;
        
        // 保存助手消息到数据库
        const lastMessage = messages.value[messages.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant' && lastMessage.content) {
            await saveMessage(lastMessage);
        }
        
        // 更新用户消息状态为已发送
        const userMessages = messages.value.filter(m => m.role === 'user');
        if (userMessages.length > 0) {
            const lastUserMessage = userMessages[userMessages.length - 1];
            lastUserMessage.status = 'sent';
            await updateMessageStatus(lastUserMessage.content, 'user', 'sent');
        }
        scrollToBottom();
    },
    onError: async (error: Error) => {
        console.error('Stream error:', error);
        isTyping.value = false;
        
        // 更新用户消息状态为错误
        const userMessages = messages.value.filter(m => m.role === 'user');
        if (userMessages.length > 0) {
            const lastUserMessage = userMessages[userMessages.length - 1];
            lastUserMessage.status = 'error';
            await updateMessageStatus(lastUserMessage.content, 'user', 'error');
        }
        
        const lastMessage = messages.value[messages.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.content) {
            messages.value.pop(); // Remove empty assistant message on error
        }
    }
});

// 滚动到底部
const scrollToBottom = () => {
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

// 测试消息函数
const testMessage = (message: string) => {
    currentMessage.value = message;
    sendMessage();
};

// 清空对话
const clearChat = async () => {
    await clearCurrentSession();
    clearData();
};

// 处理会话变更
const handleSessionChanged = async (sessionId?: number, sessionMessages?: Message[]) => {
    console.log('=== handleSessionChanged 开始 ===');
    console.log('接收到参数 - sessionId:', sessionId, 'sessionMessages长度:', sessionMessages?.length);
    clearData();
    
    // 超级强制清空当前消息列表
    messages.value.splice(0); // 清空数组的所有元素
    messages.value = []; // 重新赋值为空数组
    console.log('已强制清空消息列表，当前长度:', messages.value.length);
    
    // 等待状态更新完成
    await nextTick();
    
    // 如果传递了消息数组，直接使用；否则从数据库加载
    if (sessionMessages !== undefined) {
        console.log('使用传递的消息数组，长度:', sessionMessages.length);
        messages.value = [...sessionMessages]; // 创建新数组，避免引用问题
        console.log('设置后的消息长度:', messages.value.length);
    } else if (currentSessionId.value) {
        try {
            console.log('从数据库加载会话消息，会话ID:', currentSessionId.value);
            await loadMessages(currentSessionId.value);
            console.log('会话切换完成，消息已加载:', messages.value.length, '条消息');
        } catch (error) {
            console.error('重新加载消息失败:', error);
        }
    } else {
        console.log('当前会话ID为空，保持消息列表为空');
    }
    
    console.log('最终消息数组长度:', messages.value.length);
    console.log('最终消息数组内容:', messages.value);
    
    // 滚动到底部
    await nextTick();
    scrollToBottom();
    console.log('=== handleSessionChanged 结束 ===');
};

// 处理数据库错误
const handleDatabaseError = (error: Error) => {
    if (error.message === 'DATABASE_ERROR') {
        dbError.value = true;
    }
};

// 处理新建会话
const handleNewSession = async () => {
    console.log('=== handleNewSession 开始 ===');
    clearData();
    
    // 超级强制清空消息列表
    messages.value.splice(0); // 清空数组的所有元素
    messages.value = []; // 重新赋值为空数组
    console.log('新会话创建，已强制清空消息列表，当前长度:', messages.value.length);
    console.log('消息数组内容:', messages.value);
    
    await nextTick();
    console.log('nextTick后消息长度:', messages.value.length);
    scrollToBottom();
    console.log('=== handleNewSession 结束 ===');
};

// 准备聊天上下文
const getChatContext = () => {
    // 获取当前对话的前几条消息作为上下文，避免上下文过长
    const contextLimit = 10; // 限制上下文消息数量
    const contextMessages = messages.value.slice(-contextLimit, -1); // 排除最后一条消息（当前要发送的）
    return contextMessages.map(msg => ({
        role: msg.role,
        content: msg.content
    }));
};

// 处理键盘导航
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'ArrowUp') {
        event.preventDefault();
        if (historyIndex.value < inputHistory.value.length - 1) {
            historyIndex.value++;
            currentMessage.value = inputHistory.value[historyIndex.value];
        }
    } else if (event.key === 'ArrowDown') {
        event.preventDefault();
        if (historyIndex.value > 0) {
            historyIndex.value--;
            currentMessage.value = inputHistory.value[historyIndex.value];
        } else if (historyIndex.value === 0) {
            historyIndex.value = -1;
            currentMessage.value = '';
        }
    }
};

// 计算字数
const messageLength = computed(() => currentMessage.value.length);
const wordCount = computed(() => {
    return currentMessage.value.trim().split(/\s+/).filter(word => word.length > 0).length;
});

const sendMessage = async () => {
    if (!currentMessage.value.trim() || isFetching.value) return;

    const userMessage = currentMessage.value;
    console.log('Sending message:', userMessage);
    console.log('CSRF Token:', getCsrfToken());

    // 如果没有活跃会话，创建一个新会话
    if (!currentSessionId.value) {
        await createSession();
    }

    // 添加到输入历史
    if (userMessage.trim() && !inputHistory.value.includes(userMessage)) {
        inputHistory.value.unshift(userMessage);
        // 限制历史记录数量
        if (inputHistory.value.length > 50) {
            inputHistory.value = inputHistory.value.slice(0, 50);
        }
    }
    historyIndex.value = -1;

    const userMsg: Message = {
        role: 'user',
        content: userMessage,
        timestamp: new Date(),
        status: 'sending'
    };
    messages.value.push(userMsg);
    
    // 立即保存用户消息到数据库
    await saveMessage(userMsg);

    // Add empty assistant message for streaming
    const assistantMessage: Message = {
        role: 'assistant',
        content: '',
        timestamp: new Date()
    };
    messages.value.push(assistantMessage);

    // Clear previous stream data
    clearData();

    // 获取聊天上下文
    const context = getChatContext();

    // Send the message with context
    send({ 
        message: userMessage,
        context: context
    });

    currentMessage.value = '';

    // 滚动到底部
    nextTick(() => {
        scrollToBottom();
    });
};
</script>

<template>
    <Head title="AI Chat" />

    <ChatLayout title="AI 智能助手">
        <div class="flex h-full flex-1 gap-4 p-4">
            <!-- 会话列表侧边栏 -->
            <div 
                class="transition-all duration-300 flex-shrink-0"
                :class="showSessionList ? 'w-80' : 'w-0'"
            >
                <div 
                    class="h-full overflow-hidden transition-all duration-300"
                    :class="showSessionList ? 'opacity-100' : 'opacity-0'"
                >
                    <ChatSessionList 
                        @session-changed="handleSessionChanged"
                        @new-session="handleNewSession"
                        @database-error="handleDatabaseError"
                        :is-collapsed="!showSessionList"
                    />
                </div>
            </div>
            
            <!-- 主聊天区域 -->
            <div class="flex-1 flex flex-col">
                <Card class="flex-1 flex flex-col max-h-[calc(100vh-8rem)]">
                <CardHeader class="flex-shrink-0">
                    <div class="flex items-center justify-between">
                        <div>
                            <CardTitle class="flex items-center gap-2">
                                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                AI 智能助手
                            </CardTitle>
                            <p class="text-sm text-muted-foreground">
                                支持代码高亮、Markdown 渲染的智能对话
                            </p>
                        </div>
                        <div class="flex items-center gap-2">
                            <!-- 切换侧边栏按钮 -->
                            <Button
                                @click="showSessionList = !showSessionList"
                                variant="outline"
                                size="sm"
                                :title="showSessionList ? '隐藏会话列表' : '显示会话列表'"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path v-if="showSessionList" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                                    <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                                </svg>
                            </Button>
                            
                            <Button
                                v-if="messages.length > 0"
                                @click="clearChat"
                                variant="outline"
                                size="sm"
                                class="text-muted-foreground hover:text-destructive"
                            >
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                清空对话
                            </Button>
                            
                            <!-- 数据库重置按钮 -->
                            <Button
                                v-if="dbError"
                                @click="resetDatabase"
                                variant="outline"
                                size="sm"
                                :disabled="isResettingDb"
                                class="text-warning hover:text-warning"
                            >
                                <svg v-if="isResettingDb" class="w-4 h-4 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <svg v-else class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                {{ isResettingDb ? '重置中...' : '重置数据库' }}
                            </Button>
                        </div>
                    </div>
                </CardHeader>
                <CardContent
                    ref="messagesContainer"
                    class="flex-1 overflow-y-auto space-y-6 scroll-smooth"
                >
                    <!-- 欢迎消息 -->
                    <div v-if="messages.length === 0" class="text-center py-8">
                        <div class="text-muted-foreground mb-4">
                            <svg class="w-16 h-16 mx-auto mb-4 text-muted-foreground/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <h3 class="text-lg font-medium mb-2">开始对话</h3>
                            <p class="text-sm mb-4">我是您的 AI 助手，可以帮您解答问题、编写代码、分析文档等。</p>
                            <div class="flex gap-2 justify-center">
                                <Button @click="testMessage('Hello')" variant="outline" size="sm">
                                    测试简单消息
                                </Button>
                                <Button @click="testMessage('请写一个JavaScript函数来计算斐波那契数列')" variant="outline" size="sm">
                                    测试代码生成
                                </Button>
                            </div>
                        </div>
                    </div>

                    <!-- 消息列表 -->
                    <MessageBubble
                        v-for="(message, index) in messages"
                        :key="index"
                        :role="message.role"
                        :content="message.content"
                        :timestamp="message.timestamp"
                        :status="message.status"
                        :is-streaming="index === messages.length - 1 && message.role === 'assistant' && isStreaming"
                    />
                </CardContent>
                <CardFooter class="flex-shrink-0 border-t bg-background/50 backdrop-blur-sm">
                    <div class="w-full space-y-3">
                        <form @submit.prevent="sendMessage" class="flex w-full gap-3">
                            <div class="flex-1 relative">
                                <Input
                                    v-model="currentMessage"
                                    placeholder="输入您的问题... (支持 Markdown 格式)"
                                    :disabled="isFetching"
                                    class="pr-20 min-h-[2.5rem] resize-none"
                                    @keydown.enter.exact.prevent="sendMessage"
                                    @keydown.enter.shift.exact="currentMessage += '\n'"
                                    @keydown="handleKeyDown"
                                />
                                <div class="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                                    {{ messageLength }}/1000
                                </div>
                            </div>
                            <Button
                                type="submit"
                                :disabled="!currentMessage.trim() || isFetching"
                                class="px-6"
                            >
                                <svg v-if="isFetching" class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                <span class="ml-2">{{ isFetching ? '发送中...' : '发送' }}</span>
                            </Button>
                        </form>

                        <!-- 状态栏 -->
                        <div class="flex items-center justify-between text-xs text-muted-foreground">
                            <div class="flex items-center gap-4">
                                <span>{{ wordCount }} 词 · {{ messageLength }} 字符</span>
                                <span v-if="inputHistory.length > 0">
                                    历史记录: {{ inputHistory.length }} 条
                                </span>
                                <span v-if="isTyping" class="flex items-center gap-1 text-primary">
                                    <div class="w-2 h-2 bg-current rounded-full animate-pulse"></div>
                                    AI 正在回复...
                                </span>
                            </div>
                            <div>
                                按 ↑↓ 浏览历史 · Enter 发送 · Shift+Enter 换行
                            </div>
                        </div>
                    </div>
                </CardFooter>
                </Card>
            </div>
        </div>
    </ChatLayout>
</template>