import Dexie, { type EntityTable } from 'dexie';

// 定义聊天相关的数据类型
export interface ChatSession {
  id?: number;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface ChatMessage {
  id?: number;
  sessionId: number;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
}

// 定义数据库
export class ChatDatabase extends Dexie {
  sessions!: EntityTable<ChatSession, 'id'>;
  messages!: EntityTable<ChatMessage, 'id'>;

  constructor() {
    super('ChatDatabase');
    
    this.version(1).stores({
      sessions: '++id, title, createdAt, updatedAt, isActive',
      messages: '++id, sessionId, role, content, timestamp, status, [sessionId+role], [sessionId+timestamp]'
    });
  }
}

// 创建数据库实例
export const chatDb = new ChatDatabase();

// 清理损坏的数据
export const cleanupCorruptedData = async () => {
  try {
    console.log('开始清理损坏的数据...');
    
    // 清理会话表中的无效数据
    const sessions = await chatDb.sessions.toArray();
    for (const session of sessions) {
      if (session.id && (
        typeof session.isActive !== 'boolean' ||
        !session.title ||
        !session.createdAt ||
        !session.updatedAt
      )) {
        console.log(`删除损坏的会话: ${session.id}`);
        await chatDb.sessions.delete(session.id);
      }
    }
    
    // 清理消息表中的无效数据
    const messages = await chatDb.messages.toArray();
    for (const message of messages) {
      if (message.id && (
        !message.sessionId ||
        !message.role ||
        !message.content ||
        !message.timestamp
      )) {
        console.log(`删除损坏的消息: ${message.id}`);
        await chatDb.messages.delete(message.id);
      }
    }
    
    console.log('数据清理完成');
    return true;
  } catch (error) {
    console.error('数据清理失败:', error);
    return false;
  }
};

// 数据库错误处理和重置功能
export const resetChatDatabase = async () => {
  try {
    console.log('正在重置聊天数据库...');
    await chatDb.delete();
    console.log('旧数据库已删除');
    
    // 重新创建数据库
    const newDb = new ChatDatabase();
    console.log('新数据库已创建');
    
    // 等待数据库完全初始化
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return newDb;
  } catch (error) {
    console.error('重置数据库失败:', error);
    throw error;
  }
};

// 检查数据库是否可用
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    // 尝试执行多个基本操作
    await chatDb.sessions.limit(1).toArray();
    await chatDb.messages.limit(1).toArray();
    
    // 尝试创建一个测试记录并立即删除
    const testId = await chatDb.sessions.add({
      title: '__health_check_test__',
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: false
    });
    await chatDb.sessions.delete(testId as number);
    
    return true;
  } catch (error) {
    console.error('数据库健康检查失败:', error);
    return false;
  }
};