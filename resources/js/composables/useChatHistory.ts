import { ref, onMounted } from 'vue';
import { chatDb, type ChatSession, type ChatMessage, checkDatabaseHealth, resetChatDatabase, cleanupCorruptedData } from '@/lib/chatDatabase';

export interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: Date;
  status?: 'sending' | 'sent' | 'error';
}

export function useChatHistory() {
  const currentSessionId = ref<number | null>(null);
  const sessions = ref<ChatSession[]>([]);
  const messages = ref<Message[]>([]);
  const isLoading = ref(false);

  // 创建新的聊天会话
  const createSession = async (title?: string): Promise<number> => {
    try {
      console.log('开始创建新会话...');
      
      const newSession: Omit<ChatSession, 'id'> = {
        title: title || '新对话',
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      };

      // 将之前的会话设为非活跃状态 - 使用更安全的方式
      const activeSessions = await chatDb.sessions.toArray();
      console.log('当前所有会话:', activeSessions);
      for (const session of activeSessions) {
        if (session.isActive === true && session.id) {
          await chatDb.sessions.update(session.id, { isActive: false });
          console.log('已将会话', session.id, '设为非活跃');
        }
      }
      
      // 创建新会话
      const sessionId = await chatDb.sessions.add(newSession);
      console.log('新会话已创建，ID为:', sessionId);
      currentSessionId.value = sessionId as number;
      
      // 检查新创建的会话是否有任何消息（应该为空）
      const newSessionMessages = await chatDb.messages
        .where('sessionId')
        .equals(sessionId as number)
        .toArray();
      console.log('新创建的会话', sessionId, '的消息数:', newSessionMessages.length);
      
      if (newSessionMessages.length > 0) {
        console.error('警告：新会话不应该有消息！正在清理...', newSessionMessages);
        // 强制删除新会话中的所有消息
        await chatDb.messages.where('sessionId').equals(sessionId as number).delete();
        console.log('已清理新会话中的错误消息');
      }
      
      // 强制清空当前消息列表
      messages.value = [];
      console.log('已强制清空 messages.value，当前长度:', messages.value.length);
      
      // 刷新会话列表
      await loadSessions();
      
      return sessionId as number;
    } catch (error) {
      console.error('创建会话失败:', error);
      throw error;
    }
  };

  // 加载所有会话
  const loadSessions = async () => {
    try {
      isLoading.value = true;
      const allSessions = await chatDb.sessions
        .orderBy('updatedAt')
        .reverse()
        .toArray();
      sessions.value = allSessions;
    } catch (error) {
      console.error('加载会话失败:', error);
      // 如果是数据库错误，触发健康检查
      const isHealthy = await checkDatabaseHealth();
      if (!isHealthy) {
        console.warn('数据库检查失败，需要重置');
        throw new Error('DATABASE_ERROR');
      }
    } finally {
      isLoading.value = false;
    }
  };

  // 切换到指定会话
  const switchToSession = async (sessionId: number) => {
    console.log('switchToSession 被调用，sessionId:', sessionId);
    
    if (!sessionId || sessionId <= 0) {
      console.log('sessionId 无效，退出切换');
      return;
    }
    
    try {
      isLoading.value = true;
      
      console.log('当前活跃会话ID:', currentSessionId.value);
      
      // 将当前活跃会话设为非活跃
      if (currentSessionId.value && currentSessionId.value > 0) {
        await chatDb.sessions.update(currentSessionId.value, { isActive: false });
        console.log('已将会话', currentSessionId.value, '设为非活跃');
      }
      
      // 设置新的活跃会话
      await chatDb.sessions.update(sessionId, { 
        isActive: true,
        updatedAt: new Date()
      });
      console.log('已将会话', sessionId, '设为活跃');
      
      currentSessionId.value = sessionId;
      console.log('currentSessionId 已更新为:', currentSessionId.value);
      
      // 加载该会话的消息
      console.log('开始加载会话消息...');
      console.log('即将加载的会话ID:', sessionId);
      await loadMessages(sessionId);
      console.log('loadMessages 完成后的 messages.value 长度:', messages.value.length);
      await loadSessions();
    } catch (error) {
      console.error('切换会话失败:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // 加载会话消息
  const loadMessages = async (sessionId?: number) => {
    const targetSessionId = sessionId || currentSessionId.value;
    console.log('loadMessages 被调用，targetSessionId:', targetSessionId);
    
    if (!targetSessionId || targetSessionId <= 0) {
      console.log('targetSessionId 无效，退出加载');
      return;
    }

    try {
      // 先检查数据库中的所有消息
      const allMessages = await chatDb.messages.toArray();
      console.log('数据库中总消息数:', allMessages.length);
      console.log('所有消息的sessionId:', allMessages.map(m => m.sessionId));
      
      // 简单可靠的查询方式：先按会话ID筛选，然后在内存中排序
      const sessionMessages = await chatDb.messages
        .where('sessionId')
        .equals(targetSessionId)
        .toArray();
      
      console.log(`会话 ${targetSessionId} 的消息数:`, sessionMessages.length);
      console.log('消息详情:', sessionMessages);
      
      // 按时间戳排序（从旧到新）
      sessionMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      
      // 无论有没有消息，都要更新 messages.value
      messages.value = sessionMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp,
        status: msg.status
      }));
      
      console.log('最终设置的 messages.value:', messages.value);
      console.log('messages.value 长度:', messages.value.length);
    } catch (error) {
      console.error('加载消息失败:', error);
    }
  };

  // 保存消息到数据库
  const saveMessage = async (message: Message): Promise<void> => {
    if (!currentSessionId.value) {
      // 如果没有当前会话，创建一个新会话
      await createSession();
    }

    if (!currentSessionId.value) return;

    try {
      const dbMessage: Omit<ChatMessage, 'id'> = {
        sessionId: currentSessionId.value,
        role: message.role,
        content: message.content,
        timestamp: message.timestamp || new Date(),
        status: message.status
      };

      await chatDb.messages.add(dbMessage);
      
      // 更新会话的最后更新时间
      await chatDb.sessions.update(currentSessionId.value, {
        updatedAt: new Date()
      });

      // 如果是用户消息且会话标题还是默认的，自动生成标题
      if (message.role === 'user') {
        const session = await chatDb.sessions.get(currentSessionId.value);
        if (session && (session.title === '新对话' || session.title.startsWith('对话 '))) {
          const title = message.content.length > 30 
            ? message.content.substring(0, 30) + '...'
            : message.content;
          await chatDb.sessions.update(currentSessionId.value, { title });
        }
      }

      await loadSessions();
    } catch (error) {
      console.error('保存消息失败:', error);
    }
  };

  // 更新消息状态
  const updateMessageStatus = async (messageContent: string, role: 'user' | 'assistant', status: 'sending' | 'sent' | 'error') => {
    if (!currentSessionId.value || !messageContent) return;

    try {
      // 使用更安全的查询方式
      await chatDb.messages
        .where('sessionId')
        .equals(currentSessionId.value)
        .and(msg => msg.role === role && msg.content === messageContent)
        .modify({ status });
    } catch (error) {
      console.error('更新消息状态失败:', error);
    }
  };

  // 删除会话
  const deleteSession = async (sessionId: number) => {
    if (!sessionId || sessionId <= 0) return;
    
    try {
      // 删除会话相关的所有消息
      await chatDb.messages.where('sessionId').equals(sessionId).delete();
      // 删除会话
      await chatDb.sessions.delete(sessionId);
      
      // 如果删除的是当前会话，清空当前会话
      if (currentSessionId.value === sessionId) {
        currentSessionId.value = null;
        messages.value = [];
      }
      
      await loadSessions();
    } catch (error) {
      console.error('删除会话失败:', error);
    }
  };

  // 清空当前会话的消息
  const clearCurrentSession = async () => {
    if (!currentSessionId.value || currentSessionId.value <= 0) return;

    try {
      await chatDb.messages.where('sessionId').equals(currentSessionId.value).delete();
      messages.value = [];
      
      // 重置会话标题
      await chatDb.sessions.update(currentSessionId.value, {
        title: '新对话',
        updatedAt: new Date()
      });
      
      await loadSessions();
    } catch (error) {
      console.error('清空会话失败:', error);
    }
  };

  // 获取当前活跃会话
  const loadActiveSession = async () => {
    try {
      // 使用更健壮的查询方式
      const allSessions = await chatDb.sessions.orderBy('updatedAt').reverse().toArray();
      const activeSession = allSessions.find(session => session.isActive === true);
      
      if (activeSession && activeSession.id) {
        currentSessionId.value = activeSession.id;
        await loadMessages();
      } else if (allSessions.length > 0) {
        // 如果没有活跃会话，选择最新的会话
        const latestSession = allSessions[0];
        if (latestSession.id) {
          await switchToSession(latestSession.id);
        }
      }
    } catch (error) {
      console.error('加载活跃会话失败:', error);
      // 如果查询失败，可能是数据库问题
      const isHealthy = await checkDatabaseHealth();
      if (!isHealthy) {
        throw new Error('DATABASE_ERROR');
      }
    }
  };

  // 数据库初始化和健康检查
  const initializeDatabase = async () => {
    try {
      console.log('正在初始化数据库...');

      // 首先检查数据库是否可以正常打开
      try {
        await chatDb.open();
        console.log('数据库连接成功');
      } catch (openError) {
        console.error('数据库打开失败:', openError);
        console.log('尝试重置数据库...');
        await resetChatDatabase();
        return;
      }

      // 检查数据库健康状态
      const isHealthy = await checkDatabaseHealth();
      if (isHealthy) {
        console.log('数据库健康状态良好');
        return;
      }

      console.warn('数据库健康检查失败，尝试清理损坏的数据...');
      const cleanupSuccess = await cleanupCorruptedData();
      if (cleanupSuccess) {
        console.log('数据清理成功，重新检查健康状态...');

        const isHealthyAfterCleanup = await checkDatabaseHealth();
        if (isHealthyAfterCleanup) {
          console.log('数据库修复成功');
          return;
        }
      }

      console.warn('清理失败，尝试完全重置数据库...');
      await resetChatDatabase();
      console.log('数据库重置完成');

    } catch (error) {
      console.error('数据库初始化失败:', error);

      // 最后的尝试：强制重置数据库
      try {
        console.log('进行最后的数据库重置尝试...');
        await resetChatDatabase();
        console.log('强制重置成功');
      } catch (resetError) {
        console.error('数据库重置也失败了:', resetError);
        // 向用户界面传递错误信号
        throw new Error('DATABASE_INITIALIZATION_FAILED');
      }
    }
  };

  // 初始化
  onMounted(async () => {
    await initializeDatabase();
    await loadSessions();
    await loadActiveSession();
  });

  return {
    currentSessionId,
    sessions,
    messages,
    isLoading,
    createSession,
    loadSessions,
    switchToSession,
    loadMessages,
    saveMessage,
    updateMessageStatus,
    deleteSession,
    clearCurrentSession,
    loadActiveSession,
    initializeDatabase
  };
}